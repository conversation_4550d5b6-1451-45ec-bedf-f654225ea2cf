<!DOCTYPE html>
<html>
<head>
    <title>Time Test</title>
</head>
<body>
    <h1>Time Parsing Test</h1>
    <div id="output"></div>
    
    <script>
        // Server time format (without Z)
        const serverTime = "2025-08-19T06:37:10.686372";
        
        // Test 1: Parse as-is
        const date1 = new Date(serverTime);
        
        // Test 2: Parse with Z
        const date2 = new Date(serverTime + 'Z');
        
        // Current time
        const now = new Date();
        
        // Calculate differences
        const diff1 = now.getTime() - date1.getTime();
        const diff2 = now.getTime() - date2.getTime();
        
        const hours1 = Math.floor(diff1 / (1000 * 60 * 60));
        const hours2 = Math.floor(diff2 / (1000 * 60 * 60));
        
        const output = document.getElementById('output');
        output.innerHTML = `
            <h2>Server Time: ${serverTime}</h2>
            <h3>Without Z suffix:</h3>
            <p>Parsed: ${date1.toISOString()}</p>
            <p>Local: ${date1.toLocaleString()}</p>
            <p>Difference: ${hours1} hours ago</p>
            
            <h3>With Z suffix:</h3>
            <p>Parsed: ${date2.toISOString()}</p>
            <p>Local: ${date2.toLocaleString()}</p>
            <p>Difference: ${hours2} hours ago</p>
            
            <h3>Current Time:</h3>
            <p>Now: ${now.toISOString()}</p>
            <p>Local: ${now.toLocaleString()}</p>
        `;
    </script>
</body>
</html>