# =============================================================================
# FRONTEND ENVIRONMENT CONFIGURATION
# =============================================================================
# NOTE: When using 'make start', these values are set automatically
# Only create .env.local if deploying manually or for production

# =============================================================================
# DEFAULT CONFIGURATION (set automatically by Makefile)
# =============================================================================

# API Server Endpoints (configured dynamically)
NEXT_PUBLIC_API_BASE=http://localhost:8080
NEXT_PUBLIC_WS_BASE=ws://localhost:8080

# =============================================================================
# PRODUCTION OVERRIDES (if deploying to production)
# =============================================================================

# Create .env.local and uncomment for production deployment:
# NEXT_PUBLIC_API_BASE=https://your-api-domain.com
# NEXT_PUBLIC_WS_BASE=wss://your-api-domain.com