# =============================================================================
# CC-LOVABLE ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and configure the values below
# Required fields are marked with [REQUIRED]
# Optional fields have sensible defaults

# =============================================================================
# REQUIRED CONFIGURATION - USER MUST PROVIDE
# =============================================================================

# [REQUIRED] Anthropic API Key for Claude Code SDK
# Get your API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# OPTIONAL CONFIGURATION - ADVANCED USERS
# =============================================================================

# Encryption key for sensitive data (generate a random 32-character string)
# Leave empty to use default internal encryption
ENCRYPTION_KEY=

# =============================================================================
# DEFAULT CONFIGURATION - USUALLY NO CHANGES NEEDED
# =============================================================================

# API Server Configuration
API_PORT=8080

# Database Configuration (PostgreSQL via Docker)
POSTGRES_USER=cc
POSTGRES_PASSWORD=cc
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=cc

# Alternative: Full database URL (overrides individual POSTGRES_* variables above)
# DATABASE_URL=postgresql+psycopg://cc:cc@localhost:5432/cc

# Project Storage Paths
PROJECTS_ROOT=./data/projects
PROJECTS_ROOT_HOST=./data/projects

# Preview Server Port Range
PREVIEW_PORT_START=3100
PREVIEW_PORT_END=3999

# Claude Model Configuration
CLAUDE_CODE_MODEL=claude-sonnet-4-20250514

# Frontend API Endpoints (automatically configured by Makefile)
# Note: These are set dynamically by 'make start' - no need to change manually
NEXT_PUBLIC_API_BASE=http://localhost:8080
NEXT_PUBLIC_WS_BASE=ws://localhost:8080