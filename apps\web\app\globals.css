@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@500;800&display=swap');
@import '../styles/scrollbar.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html, body {
    height: 100%;
    overscroll-behavior: none;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', 'Segoe UI', 'Inter', system-ui, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'liga' 1, 'calt' 1;
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Light mode overrides */
  :root.light {
    --bolt-bg-primary: #ffffff;
    --bolt-bg-secondary: #f9fafb;
    --bolt-bg-tertiary: #f3f4f6;
    --bolt-border-color: rgba(139, 92, 246, 0.15);
    --bolt-text-primary: #111827;
    --bolt-text-secondary: #6b7280;
    --bolt-text-tertiary: #9ca3af;
  }
}

@layer components {
  /* Modern gradient modal background with subtle pattern */
  .modal-backdrop {
    background: 
      radial-gradient(circle at 20% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
      linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }
  
  .modal-content {
    @apply bg-white rounded-2xl shadow-2xl border border-gray-100;
    background: 
      radial-gradient(circle at 100% 0%, rgba(120, 119, 198, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 0% 100%, rgba(255, 119, 198, 0.05) 0%, transparent 50%),
      white;
  }
}

@layer utilities {
  /* Bolt.diy style color variables */
  :root {
    --gradient-opacity: 0.8;
    --primary-color: rgba(139, 92, 246, var(--gradient-opacity));
    --secondary-color: rgba(236, 72, 153, var(--gradient-opacity));
    --accent-color: rgba(59, 130, 246, var(--gradient-opacity));
    --bolt-bg-primary: #0c0a14;
    --bolt-bg-secondary: #15111e;
    --bolt-bg-tertiary: #1e1a2a;
    --bolt-border-color: rgba(139, 92, 246, 0.2);
    --bolt-text-primary: #e5e2ff;
    --bolt-text-secondary: #a8a4ce;
    --bolt-text-tertiary: #6b6685;
  }

  /* Ray container and animations for Bolt.diy style background */
  .ray-container {
    --ray-color-primary: color-mix(in srgb, var(--primary-color), transparent 30%);
    --ray-color-secondary: color-mix(in srgb, var(--secondary-color), transparent 30%);
    --ray-color-accent: color-mix(in srgb, var(--accent-color), transparent 30%);
    --ray-gradient-primary: radial-gradient(var(--ray-color-primary) 0%, transparent 70%);
    --ray-gradient-secondary: radial-gradient(var(--ray-color-secondary) 0%, transparent 70%);
    --ray-gradient-accent: radial-gradient(var(--ray-color-accent) 0%, transparent 70%);

    position: fixed;
    inset: 0;
    overflow: hidden;
    animation: fadeIn 1.5s ease-out;
    pointer-events: none;
    z-index: 0;
    mix-blend-mode: screen;
  }

  .light-ray {
    position: absolute;
    border-radius: 100%;
    mix-blend-mode: screen;
  }

  .ray-1 {
    width: 600px;
    height: 800px;
    background: var(--ray-gradient-primary);
    transform: rotate(65deg);
    top: -500px;
    left: -100px;
    filter: blur(80px);
    opacity: 0.6;
    animation: float1 15s infinite ease-in-out;
  }

  .ray-2 {
    width: 400px;
    height: 600px;
    background: var(--ray-gradient-secondary);
    transform: rotate(-30deg);
    top: -300px;
    left: 200px;
    filter: blur(60px);
    opacity: 0.6;
    animation: float2 18s infinite ease-in-out;
  }

  .ray-3 {
    width: 500px;
    height: 400px;
    background: var(--ray-gradient-accent);
    top: -320px;
    left: 500px;
    filter: blur(65px);
    opacity: 0.5;
    animation: float3 20s infinite ease-in-out;
  }

  .ray-4 {
    width: 400px;
    height: 450px;
    background: var(--ray-gradient-secondary);
    top: -350px;
    left: 800px;
    filter: blur(55px);
    opacity: 0.55;
    animation: float4 17s infinite ease-in-out;
  }

  .ray-5 {
    width: 350px;
    height: 500px;
    background: var(--ray-gradient-primary);
    transform: rotate(-45deg);
    top: -250px;
    left: 1000px;
    filter: blur(45px);
    opacity: 0.6;
    animation: float5 16s infinite ease-in-out;
  }

  .ray-6 {
    width: 300px;
    height: 700px;
    background: var(--ray-gradient-accent);
    transform: rotate(75deg);
    top: -400px;
    left: 600px;
    filter: blur(75px);
    opacity: 0.45;
    animation: float6 19s infinite ease-in-out;
  }

  .ray-7 {
    width: 450px;
    height: 600px;
    background: var(--ray-gradient-primary);
    transform: rotate(45deg);
    top: -450px;
    left: 350px;
    filter: blur(65px);
    opacity: 0.55;
    animation: float7 21s infinite ease-in-out;
  }

  .ray-8 {
    width: 380px;
    height: 550px;
    background: var(--ray-gradient-secondary);
    transform: rotate(-60deg);
    top: -380px;
    left: 750px;
    filter: blur(58px);
    opacity: 0.6;
    animation: float8 14s infinite ease-in-out;
  }

  @keyframes float1 {
    0%, 100% { transform: rotate(65deg) translate(0, 0); }
    25% { transform: rotate(70deg) translate(30px, 20px); }
    50% { transform: rotate(60deg) translate(-20px, 40px); }
    75% { transform: rotate(68deg) translate(-40px, 10px); }
  }

  @keyframes float2 {
    0%, 100% { transform: rotate(-30deg) scale(1); }
    33% { transform: rotate(-25deg) scale(1.1); }
    66% { transform: rotate(-35deg) scale(0.95); }
  }

  @keyframes float3 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(40px, 20px) rotate(5deg); }
    75% { transform: translate(-30px, 40px) rotate(-5deg); }
  }

  @keyframes float4 {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.15) rotate(10deg); }
  }

  @keyframes float5 {
    0%, 100% { transform: rotate(-45deg) translate(0, 0); }
    33% { transform: rotate(-40deg) translate(25px, -20px); }
    66% { transform: rotate(-50deg) translate(-25px, 20px); }
  }

  @keyframes float6 {
    0%, 100% { transform: rotate(75deg) scale(1); filter: blur(75px); }
    50% { transform: rotate(85deg) scale(1.1); filter: blur(65px); }
  }

  @keyframes float7 {
    0%, 100% { transform: rotate(45deg) translate(0, 0); opacity: 0.55; }
    50% { transform: rotate(40deg) translate(-30px, 30px); opacity: 0.65; }
  }

  @keyframes float8 {
    0%, 100% { transform: rotate(-60deg) scale(1); }
    25% { transform: rotate(-55deg) scale(1.05); }
    75% { transform: rotate(-65deg) scale(0.95); }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}
