{"name": "cc-lovable", "version": "1.0.0", "private": true, "description": "CC Lovable Development Environment", "workspaces": ["apps/web"], "scripts": {"dev": "npm run ensure:env && npm run ensure:venv && concurrently --raw \"npm run dev:api\" --prefix \"[WEB] \" \"npm run dev:web\"", "dev:api": "node scripts/run-api.js", "dev:web": "node scripts/run-web.js", "ensure:env": "node scripts/setup-env.js", "ensure:venv": "node scripts/setup-venv.js", "postinstall": "npm run ensure:env && npm run ensure:venv", "setup": "npm install", "clean": "node scripts/clean.js", "db:reset": "node scripts/reset-db.js", "db:backup": "node scripts/backup-db.js"}, "devDependencies": {"concurrently": "^8.2.2", "dotenv": "^16.4.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"react-icons": "^5.5.0"}}