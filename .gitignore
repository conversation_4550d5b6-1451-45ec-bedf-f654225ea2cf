# Environment variables
.env
.env.local
.env.development
.env.production

# Python
__pycache__/
*.py[cod]
*.so
*.egg
*.egg-info/
venv/
env/
.venv/
.python-version
uv.lock

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/

# Next.js
.next/
out/
*.tsbuildinfo
next-env.d.ts

# Build outputs
dist/
build/
coverage/

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Docker
.dockerignore

# Testing
.coverage
.pytest_cache/
.cache/

# Temporary files
*.tmp
*.temp
.tmp/
.env.ports
.env.local

# Backup files
backup/
*.backup
*.bak

# Development files
output.txt
result.md

# Docker volumes
docker-data/

# Project workspaces
data/
docs/

.claude/