{"name": "cc-lovable-web", "version": "0.1.0", "private": true, "scripts": {"dev": "node scripts/dev.js", "dev:no-open": "next dev --turbo", "build": "next build", "start": "next start"}, "dependencies": {"clsx": "2.1.1", "critters": "^0.0.23", "framer-motion": "11.3.31", "highlight.js": "11.9.0", "lucide-react": "^0.540.0", "next": "14.2.5", "open": "^10.2.0", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@types/node": "24.2.1", "@types/react-syntax-highlighter": "^15.5.0", "autoprefixer": "10.4.20", "postcss": "8.4.41", "tailwindcss": "3.4.10", "typescript": "5.9.2"}, "engines": {"node": ">=18"}}